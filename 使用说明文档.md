# 窗口管理工具使用说明

## 功能概述

这是一个用于管理Windows窗口位置和大小的Python GUI工具，主要功能包括：

1. **获取窗口坐标**：根据窗口标题获取窗口的当前位置和大小
2. **重置窗口位置**：将指定窗口移动到新的位置并调整大小
3. **配置记忆**：自动保存上次使用的窗口标题和坐标设置

## 安装要求

### 系统要求
- Windows 操作系统
- Python 3.6 或更高版本

### 依赖安装
在项目目录下运行以下命令安装依赖：

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install pywin32 pyperclip
```

## 使用方法

### 启动程序
```bash
python window_manager.py
```

### 主要功能

#### 1. 获取窗口坐标
1. 在"获取窗口坐标"区域的"窗口标题"输入框中输入目标窗口的标题（支持部分匹配）
2. 点击"获取坐标"按钮
3. 程序会在"窗口坐标"框中显示找到的窗口坐标（格式：x1,y1,x2,y2）
4. 点击"复制"按钮可将坐标复制到剪贴板

#### 2. 重置窗口位置和大小
1. 在"重置窗口位置和大小"区域的"窗口标题"输入框中输入目标窗口标题
2. 在"新坐标"输入框中输入新的窗口坐标（格式：x1,y1,x2,y2）
   - x1, y1：窗口左上角坐标
   - x2, y2：窗口右下角坐标
3. 点击"重置窗口"按钮执行重置操作

#### 3. 状态信息
程序底部的状态信息区域会显示：
- 操作日志
- 错误信息
- 成功提示

## 坐标格式说明

坐标使用逗号分隔的四个整数表示：`x1,y1,x2,y2`

- `x1, y1`：窗口左上角的屏幕坐标
- `x2, y2`：窗口右下角的屏幕坐标

例如：`100,100,900,700` 表示窗口左上角在(100,100)，右下角在(900,700)，窗口大小为800x600像素。

## 配置文件

程序会自动创建 `window_config.json` 文件来保存：
- 上次使用的窗口标题
- 上次使用的坐标设置

这样下次启动程序时会自动填入上次的设置，提高使用效率。

## 注意事项

1. **权限要求**：程序需要访问其他应用程序的窗口信息，某些安全软件可能会提示权限请求
2. **窗口查找**：程序通过窗口标题进行模糊匹配，如果有多个匹配的窗口，会使用第一个找到的窗口
3. **坐标范围**：请确保设置的坐标在屏幕范围内，否则窗口可能显示异常
4. **目标程序**：确保目标程序（如"向僵尸开炮"）正在运行且窗口可见

## 常见问题

### Q: 提示"未找到窗口"
A: 请检查：
- 目标程序是否正在运行
- 窗口标题是否正确（支持部分匹配）
- 窗口是否被最小化（程序只能找到可见窗口）

### Q: 重置窗口后位置不正确
A: 请检查：
- 坐标格式是否正确（x1,y1,x2,y2）
- 坐标值是否在屏幕范围内
- x2 > x1 且 y2 > y1（确保窗口大小为正数）

### Q: 复制功能不工作
A: 请确保已正确安装 `pyperclip` 库，某些系统可能需要额外的剪贴板支持。

## 技术支持

如遇到问题，请检查：
1. Python版本是否符合要求
2. 依赖库是否正确安装
3. 程序状态信息区域的错误提示

程序使用了以下主要技术：
- `tkinter`：GUI界面
- `win32gui`：Windows窗口操作
- `pyperclip`：剪贴板操作
- `json`：配置文件处理