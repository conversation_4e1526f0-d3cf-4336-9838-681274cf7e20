import tkinter as tk
from tkinter import ttk, messagebox
import win32gui
import win32con
import json
import os
import pyperclip

class WindowManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("窗口管理工具")
        self.root.geometry("500x400")
        
        self.config_file = "window_config.json"
        self.load_config()
        
        self.setup_ui()
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {
                    "last_title": "向僵尸开炮",
                    "last_coordinates": "0,0,800,600"
                }
        except:
            self.config = {
                "last_title": "向僵尸开炮",
                "last_coordinates": "0,0,800,600"
            }
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 获取窗口部分
        get_frame = ttk.LabelFrame(self.root, text="获取窗口坐标", padding=10)
        get_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(get_frame, text="窗口标题:").grid(row=0, column=0, sticky="w", pady=2)
        self.title_entry1 = ttk.Entry(get_frame, width=30)
        self.title_entry1.grid(row=0, column=1, padx=5, pady=2)
        self.title_entry1.insert(0, self.config.get("last_title", ""))
        
        ttk.Button(get_frame, text="获取坐标", command=self.get_window_coords).grid(row=0, column=2, padx=5, pady=2)
        
        ttk.Label(get_frame, text="窗口坐标:").grid(row=1, column=0, sticky="w", pady=2)
        self.coords_var = tk.StringVar()
        self.coords_entry = ttk.Entry(get_frame, textvariable=self.coords_var, width=30, state="readonly")
        self.coords_entry.grid(row=1, column=1, padx=5, pady=2)
        
        ttk.Button(get_frame, text="复制", command=self.copy_coords).grid(row=1, column=2, padx=5, pady=2)
        
        # 重置窗口部分
        reset_frame = ttk.LabelFrame(self.root, text="重置窗口位置和大小", padding=10)
        reset_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(reset_frame, text="窗口标题:").grid(row=0, column=0, sticky="w", pady=2)
        self.title_entry2 = ttk.Entry(reset_frame, width=30)
        self.title_entry2.grid(row=0, column=1, padx=5, pady=2)
        self.title_entry2.insert(0, self.config.get("last_title", ""))
        
        ttk.Label(reset_frame, text="新坐标 (x1,y1,x2,y2):").grid(row=1, column=0, sticky="w", pady=2)
        self.new_coords_entry = ttk.Entry(reset_frame, width=30)
        self.new_coords_entry.grid(row=1, column=1, padx=5, pady=2)
        self.new_coords_entry.insert(0, self.config.get("last_coordinates", ""))
        
        ttk.Button(reset_frame, text="重置窗口", command=self.reset_window).grid(row=1, column=2, padx=5, pady=2)
        
        # 状态显示
        status_frame = ttk.LabelFrame(self.root, text="状态信息", padding=10)
        status_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.status_text = tk.Text(status_frame, height=8, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(status_frame, orient="vertical", command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.log_message("窗口管理工具已启动")
    
    def log_message(self, message):
        """记录日志消息"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.root.update()
    
    def find_window_by_title(self, title):
        """根据标题查找窗口"""
        def enum_windows_proc(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if title in window_title:
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_proc, windows)
        return windows
    
    def get_window_coords(self):
        """获取窗口坐标"""
        title = self.title_entry1.get().strip()
        if not title:
            messagebox.showwarning("警告", "请输入窗口标题")
            return
        
        try:
            windows = self.find_window_by_title(title)
            if not windows:
                self.log_message(f"未找到标题包含 '{title}' 的窗口")
                messagebox.showwarning("警告", f"未找到标题包含 '{title}' 的窗口")
                return
            
            if len(windows) > 1:
                self.log_message(f"找到多个匹配的窗口:")
                for hwnd, window_title in windows:
                    self.log_message(f"  - {window_title}")
            
            # 使用第一个匹配的窗口
            hwnd, window_title = windows[0]
            rect = win32gui.GetWindowRect(hwnd)
            coords = f"{rect[0]},{rect[1]},{rect[2]},{rect[3]}"
            
            self.coords_var.set(coords)
            self.log_message(f"获取窗口 '{window_title}' 坐标: {coords}")
            
            # 保存配置
            self.config["last_title"] = title
            self.save_config()
            
        except Exception as e:
            error_msg = f"获取窗口坐标失败: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def copy_coords(self):
        """复制坐标到剪贴板"""
        coords = self.coords_var.get()
        if coords:
            try:
                pyperclip.copy(coords)
                self.log_message(f"坐标已复制到剪贴板: {coords}")
                messagebox.showinfo("成功", "坐标已复制到剪贴板")
            except Exception as e:
                error_msg = f"复制失败: {str(e)}"
                self.log_message(error_msg)
                messagebox.showerror("错误", error_msg)
        else:
            messagebox.showwarning("警告", "没有可复制的坐标")
    
    def reset_window(self):
        """重置窗口位置和大小"""
        title = self.title_entry2.get().strip()
        coords_str = self.new_coords_entry.get().strip()
        
        if not title:
            messagebox.showwarning("警告", "请输入窗口标题")
            return
        
        if not coords_str:
            messagebox.showwarning("警告", "请输入新坐标")
            return
        
        try:
            # 解析坐标
            coords = [int(x.strip()) for x in coords_str.split(',')]
            if len(coords) != 4:
                raise ValueError("坐标格式错误")
            
            x1, y1, x2, y2 = coords
            width = x2 - x1
            height = y2 - y1
            
            if width <= 0 or height <= 0:
                raise ValueError("窗口大小必须为正数")
            
            # 查找窗口
            windows = self.find_window_by_title(title)
            if not windows:
                self.log_message(f"未找到标题包含 '{title}' 的窗口")
                messagebox.showwarning("警告", f"未找到标题包含 '{title}' 的窗口")
                return
            
            # 重置第一个匹配的窗口
            hwnd, window_title = windows[0]
            
            # 移动和调整窗口大小
            win32gui.SetWindowPos(
                hwnd, 
                win32con.HWND_TOP, 
                x1, y1, width, height, 
                win32con.SWP_SHOWWINDOW
            )
            
            self.log_message(f"窗口 '{window_title}' 已重置到: {coords_str}")
            messagebox.showinfo("成功", f"窗口已重置到指定位置和大小")
            
            # 保存配置
            self.config["last_title"] = title
            self.config["last_coordinates"] = coords_str
            self.save_config()
            
        except ValueError as e:
            error_msg = f"坐标格式错误: {str(e)}\n请使用格式: x1,y1,x2,y2"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
        except Exception as e:
            error_msg = f"重置窗口失败: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def run(self):
        """运行应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = WindowManager()
    app.run()